'use client'

import { AdminProvider, useAdmin } from '@/lib/admin-context'
import { AdminLogin } from '@/components/admin/AdminLogin'
import { AdminDashboard } from '@/components/admin/AdminDashboard'
import { Toaster } from '@/components/ui/toaster'

function AdminPageContent() {
  const { isAuthenticated } = useAdmin()

  return (
    <>
      {isAuthenticated ? <AdminDashboard /> : <AdminLogin />}
      <Toaster />
    </>
  )
}

export default function AdminPage() {
  return (
    <AdminProvider>
      <AdminPageContent />
    </AdminProvider>
  )
}
