'use client'

import { useState } from 'react'
import { Camera, Mail, Phone, Shield, FileText, Instagram, Facebook, Twitter, Settings } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { AdminProvider, useAdmin, Photo, Category } from '@/lib/admin-context'
import { Toaster } from '@/components/ui/toaster'
import Link from 'next/link'

function HomePage() {
  const { categories, generateAlipayQR } = useAdmin()
  const [selectedCategory, setSelectedCategory] = useState<string>('landscapes')
  const [isPaymentOpen, setIsPaymentOpen] = useState(false)
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)
  const [email, setEmail] = useState('')
  const [qrCodeImage, setQrCodeImage] = useState<string>('')
  const [isGeneratingQR, setIsGeneratingQR] = useState(false)

  const handlePhotoClick = (photo: Photo) => {
    setSelectedPhoto(photo)
    setIsPaymentOpen(true)
    setQrCodeImage('') // Reset QR code
  }

  const handleGenerateQR = async () => {
    if (!selectedPhoto || !email) return

    setIsGeneratingQR(true)
    try {
      const qrCode = await generateAlipayQR(selectedPhoto.id, email)
      setQrCodeImage(qrCode)
    } catch (error) {
      alert('Failed to generate QR code. Please try again.')
    }
    setIsGeneratingQR(false)
  }

  const handlePayment = () => {
    // In a real implementation, this would handle the payment verification
    alert(`Payment verification completed for ${selectedPhoto?.title}. High-resolution image will be sent to: ${email}`)
    setIsPaymentOpen(false)
    setEmail('')
    setQrCodeImage('')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <Camera className="h-8 w-8 text-gray-900" />
              <h1 className="text-2xl font-bold text-gray-900">PhotoArt Studio</h1>
            </div>
            <nav className="hidden md:flex space-x-8 items-center">
              <a
                href="#portfolio"
                className="text-gray-600 hover:text-gray-900 transition-colors"
                onClick={(e) => {
                  e.preventDefault()
                  document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })
                }}
              >
                Portfolio
              </a>
              <a
                href="#about"
                className="text-gray-600 hover:text-gray-900 transition-colors"
                onClick={(e) => {
                  e.preventDefault()
                  document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' })
                }}
              >
                About
              </a>
              <a
                href="#contact"
                className="text-gray-600 hover:text-gray-900 transition-colors"
                onClick={(e) => {
                  e.preventDefault()
                  document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })
                }}
              >
                Contact
              </a>
              <Link
                href="/admin"
                className="text-gray-500 hover:text-orange-500 transition-colors"
                title="Admin Panel"
              >
                <Settings className="h-5 w-5" />
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-black text-white">
        <div className="absolute inset-0">
          <img
            src="https://zenfolio.com/wp-content/uploads/2023/03/Landscape-Photography-solutions-website-example-on-desktop-and-mobile.png"
            alt="Hero"
            className="w-full h-full object-cover opacity-40"
          />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
          <div className="text-center">
            <h2 className="text-5xl md:text-6xl font-bold mb-6">
              Capturing Life's
              <span className="block text-orange-400">Beautiful Moments</span>
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Professional photography across landscapes, portraits, food, wildlife, architecture, and artistic realism.
              Discover our collection of free and premium photography works.
            </p>
            <Button
              size="lg"
              className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3"
              onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Explore Portfolio
            </Button>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Photography Portfolio</h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore our diverse collection of photography spanning multiple genres.
              Free samples available, premium collections require purchase.
            </p>
          </div>

          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid w-full grid-cols-3 md:grid-cols-6 mb-8">
              {categories.map((category) => (
                <TabsTrigger key={category.id} value={category.id} className="text-sm">
                  {category.name}
                </TabsTrigger>
              ))}
            </TabsList>

            {categories.map((category) => (
              <TabsContent key={category.id} value={category.id} className="space-y-6">
                <div className="text-center">
                  <h4 className="text-2xl font-semibold text-gray-900 mb-2">{category.name}</h4>
                  <p className="text-gray-600">{category.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {category.photos.map((photo) => (
                    <Card
                      key={photo.id}
                      className="group cursor-pointer transform hover:scale-105 transition-all duration-300"
                      onClick={() => handlePhotoClick(photo)}
                    >
                      <div className="relative">
                        <img
                          src={photo.src}
                          alt={photo.title}
                          className="w-full h-64 object-cover rounded-t-lg"
                        />
                        {photo.isPaid && (
                          <Badge className="absolute top-2 right-2 bg-orange-500">
                            ${photo.price}
                          </Badge>
                        )}
                        {!photo.isPaid && (
                          <Badge className="absolute top-2 right-2 bg-green-500">
                            Free
                          </Badge>
                        )}
                      </div>
                      <CardContent className="p-4">
                        <h5 className="font-semibold text-gray-900">{photo.title}</h5>
                        <p className="text-sm text-gray-600 mt-1">
                          {photo.isPaid ? 'Premium Collection' : 'Free Sample'}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">About PhotoArt Studio</h3>
              <p className="text-lg text-gray-600 mb-6">
                We are a professional photography studio specializing in capturing the beauty of life through our lens.
                With over 10 years of experience, we offer both free sample works and premium collections across
                multiple photography genres.
              </p>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Camera className="h-5 w-5 text-orange-500" />
                  <span className="text-gray-700">Professional Equipment & Expertise</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-orange-500" />
                  <span className="text-gray-700">High-Quality Digital Delivery</span>
                </div>
                <div className="flex items-center space-x-3">
                  <FileText className="h-5 w-5 text-orange-500" />
                  <span className="text-gray-700">Commercial Usage Rights Available</span>
                </div>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=600"
                alt="Photographer at work"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Contact & Policies */}
      <section id="contact" className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Mail className="h-5 w-5" />
                  <span>Contact Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">+****************</span>
                </div>
                <div className="flex space-x-4 mt-4">
                  <Instagram className="h-5 w-5 text-gray-500 hover:text-orange-500 cursor-pointer" />
                  <Facebook className="h-5 w-5 text-gray-500 hover:text-orange-500 cursor-pointer" />
                  <Twitter className="h-5 w-5 text-gray-500 hover:text-orange-500 cursor-pointer" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment & Delivery</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-3">
                  We accept payments via Alipay. After successful payment, high-resolution images
                  will be delivered to your email within 24 hours.
                </p>
                <div className="text-xs text-gray-500">
                  • Digital downloads only
                  • Commercial license available
                  • 30-day download guarantee
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Refund & Dispute Policy</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-3">
                  We offer a 7-day refund policy for unsatisfactory purchases.
                  Contact our customer service for any issues or disputes.
                </p>
                <div className="text-xs text-gray-500">
                  • Full refund within 7 days
                  • Quality guarantee
                  • 24/7 customer support
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Camera className="h-6 w-6" />
              <span className="text-lg font-semibold">PhotoArt Studio</span>
            </div>
            <div className="text-sm text-gray-400">
              © 2024 PhotoArt Studio. All rights reserved.
            </div>
          </div>
        </div>
      </footer>

      {/* Photo Viewer/Payment Dialog */}
      <Dialog open={isPaymentOpen} onOpenChange={setIsPaymentOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>
              {selectedPhoto?.isPaid ? 'Purchase Premium Photo' : 'Photo Preview'}
            </DialogTitle>
            <DialogDescription>
              {selectedPhoto?.isPaid
                ? 'Complete your purchase to receive the high-resolution image via email.'
                : 'This is a free sample. Click download to get the full resolution image.'
              }
            </DialogDescription>
          </DialogHeader>

          {selectedPhoto && (
            <div className="space-y-4">
              <div className="text-center">
                <img
                  src={selectedPhoto.src}
                  alt={selectedPhoto.title}
                  className="w-full h-48 object-cover rounded-lg mb-3 shadow-md"
                />
                <h4 className="text-lg font-semibold">{selectedPhoto.title}</h4>
                {selectedPhoto.isPaid ? (
                  <p className="text-2xl font-bold text-orange-500">${selectedPhoto.price}</p>
                ) : (
                  <Badge className="bg-green-500 text-white">Free Download</Badge>
                )}
              </div>

              {selectedPhoto.isPaid ? (
                <>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email Address</label>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-lg text-center border">
                    <p className="text-sm text-gray-700 mb-3 font-medium">Scan QR Code to Pay with Alipay</p>
                    <div className="w-40 h-40 mx-auto bg-white rounded-lg flex items-center justify-center shadow-md border-2 border-blue-200">
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">支付宝付款</div>
                        <div className="w-24 h-24 bg-gray-100 rounded border-2 border-dashed border-gray-300 flex items-center justify-center mb-2">
                          <div className="text-xs text-gray-400 text-center">
                            QR<br/>CODE
                          </div>
                        </div>
                        <div className="text-sm font-bold text-blue-600">${selectedPhoto.price}</div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-3">
                      Scan with Alipay app to complete payment
                    </p>
                  </div>

                  <Button
                    onClick={handlePayment}
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3"
                    disabled={!email}
                  >
                    Confirm Payment & Email Delivery
                  </Button>
                </>
              ) : (
                <div className="space-y-3">
                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <p className="text-sm text-green-700 text-center">
                      This high-quality image is free to download and use.
                    </p>
                  </div>
                  <Button
                    onClick={() => {
                      // In a real implementation, this would trigger download
                      alert(`Downloading ${selectedPhoto.title}...`)
                      setIsPaymentOpen(false)
                    }}
                    className="w-full bg-green-500 hover:bg-green-600 text-white py-3"
                  >
                    Download High-Resolution Image
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
