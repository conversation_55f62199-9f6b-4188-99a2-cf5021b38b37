'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useToast } from '@/hooks/use-toast'

export interface Photo {
  id: string
  src: string
  title: string
  isPaid: boolean
  price?: number
  category: string
  description?: string
  createdAt: string
  updatedAt: string
}

export interface Category {
  id: string
  name: string
  description: string
  coverImage: string
  photos: Photo[]
}

interface AdminContextType {
  isAuthenticated: boolean
  photos: Photo[]
  categories: Category[]
  login: (password: string) => boolean
  logout: () => void
  addPhoto: (photo: Omit<Photo, 'id' | 'createdAt' | 'updatedAt'>) => void
  updatePhoto: (id: string, updates: Partial<Photo>) => void
  deletePhoto: (id: string) => void
  updatePhotoPrice: (id: string, price: number) => void
  generateAlipayQR: (photoId: string, email: string) => Promise<string>
}

const AdminContext = createContext<AdminContextType | undefined>(undefined)

// Default admin password (in production, this should be properly hashed and stored securely)
const ADMIN_PASSWORD = 'photography2024'

// Initial data
const initialCategories: Category[] = [
  {
    id: 'landscapes',
    name: 'Landscapes',
    description: 'Breathtaking natural scenery and outdoor photography',
    coverImage: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
    photos: [
      {
        id: '1',
        src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
        title: 'Mountain Vista',
        isPaid: false,
        category: 'landscapes',
        description: 'Stunning mountain landscape at sunrise',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        src: 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=800',
        title: 'Forest Path',
        isPaid: false,
        category: 'landscapes',
        description: 'Peaceful forest walking trail',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '3',
        src: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800',
        title: 'Ocean Waves',
        isPaid: true,
        price: 25,
        category: 'landscapes',
        description: 'Dynamic ocean waves crashing on rocks',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '4',
        src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
        title: 'Desert Sunset',
        isPaid: true,
        price: 30,
        category: 'landscapes',
        description: 'Golden hour in the desert',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
    ]
  },
  {
    id: 'people',
    name: 'People',
    description: 'Professional portraits and lifestyle photography',
    coverImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800',
    photos: [
      {
        id: '5',
        src: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800',
        title: 'Business Portrait',
        isPaid: false,
        category: 'people',
        description: 'Professional business headshot',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '6',
        src: 'https://images.unsplash.com/photo-1494790108755-2616b612b77c?w=800',
        title: 'Natural Light Portrait',
        isPaid: false,
        category: 'people',
        description: 'Natural light portrait photography',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '7',
        src: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=800',
        title: 'Creative Portrait',
        isPaid: true,
        price: 35,
        category: 'people',
        description: 'Artistic portrait with creative lighting',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '8',
        src: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=800',
        title: 'Lifestyle Shot',
        isPaid: true,
        price: 40,
        category: 'people',
        description: 'Candid lifestyle photography',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
    ]
  },
  // Add other categories with similar structure...
  {
    id: 'food',
    name: 'Food',
    description: 'Delicious culinary photography and food styling',
    coverImage: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800',
    photos: [
      {
        id: '9',
        src: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800',
        title: 'Pizza Perfection',
        isPaid: false,
        category: 'food',
        description: 'Artisanal pizza photography',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '10',
        src: 'https://images.unsplash.com/photo-1540189549336-e6e99c3679fe?w=800',
        title: 'Fresh Salad',
        isPaid: false,
        category: 'food',
        description: 'Fresh garden salad composition',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '11',
        src: 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=800',
        title: 'Gourmet Dessert',
        isPaid: true,
        price: 28,
        category: 'food',
        description: 'Elegant dessert presentation',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '12',
        src: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=800',
        title: 'Fine Dining',
        isPaid: true,
        price: 32,
        category: 'food',
        description: 'Fine dining plate presentation',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
    ]
  },
  {
    id: 'animals',
    name: 'Animals',
    description: 'Wildlife and pet photography',
    coverImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',
    photos: [
      {
        id: '13',
        src: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',
        title: 'Wild Tiger',
        isPaid: false,
        category: 'animals',
        description: 'Majestic tiger in natural habitat',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '14',
        src: 'https://images.unsplash.com/photo-1425082661705-1834bfd09dca?w=800',
        title: 'Loyal Dog',
        isPaid: false,
        category: 'animals',
        description: 'Pet portrait photography',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '15',
        src: 'https://images.unsplash.com/photo-1564349683136-77e08dba1ef7?w=800',
        title: 'Majestic Eagle',
        isPaid: true,
        price: 45,
        category: 'animals',
        description: 'Eagle in flight wildlife photography',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '16',
        src: 'https://images.unsplash.com/photo-1548247416-ec66f4900b2e?w=800',
        title: 'Cat Portrait',
        isPaid: true,
        price: 20,
        category: 'animals',
        description: 'Artistic cat portrait',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
    ]
  },
  {
    id: 'architecture',
    name: 'Architecture',
    description: 'Urban landscapes and architectural details',
    coverImage: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800',
    photos: [
      {
        id: '17',
        src: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800',
        title: 'Modern Building',
        isPaid: false,
        category: 'architecture',
        description: 'Contemporary architectural photography',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '18',
        src: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800',
        title: 'City Skyline',
        isPaid: false,
        category: 'architecture',
        description: 'Urban skyline at twilight',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '19',
        src: 'https://images.unsplash.com/photo-1461863109726-246fa9598dc3?w=800',
        title: 'Historic Cathedral',
        isPaid: true,
        price: 38,
        category: 'architecture',
        description: 'Gothic cathedral architecture',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '20',
        src: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800',
        title: 'Bridge Design',
        isPaid: true,
        price: 33,
        category: 'architecture',
        description: 'Modern bridge engineering',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
    ]
  },
  {
    id: 'realism',
    name: 'Realism',
    description: 'Artistic and conceptual photography',
    coverImage: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=800',
    photos: [
      {
        id: '21',
        src: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=800',
        title: 'Abstract Light',
        isPaid: false,
        category: 'realism',
        description: 'Abstract light and shadow study',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '22',
        src: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800',
        title: 'Conceptual Art',
        isPaid: false,
        category: 'realism',
        description: 'Conceptual photography artwork',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '23',
        src: 'https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?w=800',
        title: 'Urban Reality',
        isPaid: true,
        price: 42,
        category: 'realism',
        description: 'Urban street photography',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '24',
        src: 'https://images.unsplash.com/photo-1511593358241-7eea1f3c84e5?w=800',
        title: 'Street Scene',
        isPaid: true,
        price: 36,
        category: 'realism',
        description: 'Candid street photography',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
    ]
  }
]

export function AdminProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [categories, setCategories] = useState<Category[]>(initialCategories)
  const { toast } = useToast()

  // Get all photos from all categories
  const photos = categories.flatMap(category => category.photos)

  useEffect(() => {
    // Check if user is already authenticated
    const authStatus = localStorage.getItem('admin_authenticated')
    if (authStatus === 'true') {
      setIsAuthenticated(true)
    }
  }, [])

  const login = (password: string): boolean => {
    if (password === ADMIN_PASSWORD) {
      setIsAuthenticated(true)
      localStorage.setItem('admin_authenticated', 'true')
      toast({
        title: "Login Successful",
        description: "Welcome to the admin panel!",
      })
      return true
    } else {
      toast({
        title: "Login Failed",
        description: "Invalid password. Please try again.",
        variant: "destructive",
      })
      return false
    }
  }

  const logout = () => {
    setIsAuthenticated(false)
    localStorage.removeItem('admin_authenticated')
    toast({
      title: "Logged Out",
      description: "You have been logged out successfully.",
    })
  }

  const addPhoto = (photoData: Omit<Photo, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newPhoto: Photo = {
      ...photoData,
      id: Math.random().toString(36).substr(2, 9),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    setCategories(prev => prev.map(category =>
      category.id === photoData.category
        ? { ...category, photos: [...category.photos, newPhoto] }
        : category
    ))

    toast({
      title: "Photo Added",
      description: `"${newPhoto.title}" has been added to ${photoData.category}.`,
    })
  }

  const updatePhoto = (id: string, updates: Partial<Photo>) => {
    setCategories(prev => prev.map(category => ({
      ...category,
      photos: category.photos.map(photo =>
        photo.id === id
          ? { ...photo, ...updates, updatedAt: new Date().toISOString() }
          : photo
      )
    })))

    toast({
      title: "Photo Updated",
      description: "Photo details have been updated successfully.",
    })
  }

  const deletePhoto = (id: string) => {
    const photoToDelete = photos.find(p => p.id === id)

    setCategories(prev => prev.map(category => ({
      ...category,
      photos: category.photos.filter(photo => photo.id !== id)
    })))

    toast({
      title: "Photo Deleted",
      description: `"${photoToDelete?.title}" has been deleted.`,
    })
  }

  const updatePhotoPrice = (id: string, price: number) => {
    updatePhoto(id, { price, isPaid: price > 0 })
  }

  // Simulate Alipay QR code generation
  const generateAlipayQR = async (photoId: string, email: string): Promise<string> => {
    const photo = photos.find(p => p.id === photoId)
    if (!photo) throw new Error('Photo not found')

    // In a real implementation, this would call Alipay's API
    // For demo purposes, we'll generate a QR code with payment details
    const QRCode = await import('qrcode')

    const paymentData = {
      merchant_id: 'photoart_studio_2024',
      amount: photo.price,
      currency: 'USD',
      order_id: `order_${photoId}_${Date.now()}`,
      product_name: photo.title,
      customer_email: email,
      return_url: 'https://photoartstudio.com/payment/success',
      notify_url: 'https://photoartstudio.com/api/payment/notify'
    }

    // Generate QR code containing payment information
    const qrCodeDataURL = await QRCode.toDataURL(JSON.stringify(paymentData), {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })

    return qrCodeDataURL
  }

  const contextValue: AdminContextType = {
    isAuthenticated,
    photos,
    categories,
    login,
    logout,
    addPhoto,
    updatePhoto,
    deletePhoto,
    updatePhotoPrice,
    generateAlipayQR,
  }

  return (
    <AdminContext.Provider value={contextValue}>
      {children}
    </AdminContext.Provider>
  )
}

export function useAdmin() {
  const context = useContext(AdminContext)
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider')
  }
  return context
}
