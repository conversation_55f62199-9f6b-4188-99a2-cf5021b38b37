'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Camera, LogOut, Plus, Edit, Trash2, DollarSign, ImageIcon, BarChart3 } from 'lucide-react'
import { useAdmin, Photo } from '@/lib/admin-context'

export function AdminDashboard() {
  const { logout, photos, categories, addPhoto, updatePhoto, deletePhoto, updatePhotoPrice } = useAdmin()
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingPhoto, setEditingPhoto] = useState<Photo | null>(null)

  // Statistics
  const totalPhotos = photos.length
  const paidPhotos = photos.filter(p => p.isPaid).length
  const freePhotos = photos.filter(p => !p.isPaid).length
  const totalRevenue = photos.filter(p => p.isPaid).reduce((sum, p) => sum + (p.price || 0), 0)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <Camera className="h-8 w-8 text-orange-500" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
                <p className="text-sm text-gray-500">PhotoArt Studio Management</p>
              </div>
            </div>
            <Button
              onClick={logout}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <LogOut className="h-4 w-4" />
              <span>Logout</span>
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Photos</CardTitle>
              <ImageIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalPhotos}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Premium Photos</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{paidPhotos}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Free Samples</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{freePhotos}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${totalRevenue}</div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="photos" className="space-y-6">
          <TabsList>
            <TabsTrigger value="photos">Photo Management</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="pricing">Pricing</TabsTrigger>
          </TabsList>

          {/* Photo Management Tab */}
          <TabsContent value="photos" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Photo Management</h2>
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-orange-500 hover:bg-orange-600">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Photo
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <AddPhotoForm onClose={() => setIsAddDialogOpen(false)} />
                </DialogContent>
              </Dialog>
            </div>

            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Image</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {photos.map((photo) => (
                      <TableRow key={photo.id}>
                        <TableCell>
                          <img
                            src={photo.src}
                            alt={photo.title}
                            className="w-16 h-16 object-cover rounded"
                          />
                        </TableCell>
                        <TableCell className="font-medium">{photo.title}</TableCell>
                        <TableCell className="capitalize">{photo.category}</TableCell>
                        <TableCell>
                          <Badge variant={photo.isPaid ? "default" : "secondary"}>
                            {photo.isPaid ? 'Premium' : 'Free'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {photo.isPaid ? `$${photo.price}` : 'Free'}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingPhoto(photo)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => deletePhoto(photo.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-6">
            <h2 className="text-xl font-semibold">Categories Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categories.map((category) => (
                <Card key={category.id}>
                  <CardHeader>
                    <CardTitle className="capitalize">{category.name}</CardTitle>
                    <CardDescription>{category.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Total Photos:</span>
                        <span className="font-medium">{category.photos.length}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Premium:</span>
                        <span className="font-medium">
                          {category.photos.filter(p => p.isPaid).length}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Free:</span>
                        <span className="font-medium">
                          {category.photos.filter(p => !p.isPaid).length}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Pricing Tab */}
          <TabsContent value="pricing" className="space-y-6">
            <h2 className="text-xl font-semibold">Pricing Management</h2>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Photo</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>Current Price</TableHead>
                      <TableHead>Update Price</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {photos.filter(p => p.isPaid).map((photo) => (
                      <PricingRow key={photo.id} photo={photo} onUpdatePrice={updatePhotoPrice} />
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Edit Photo Dialog */}
      {editingPhoto && (
        <EditPhotoDialog
          photo={editingPhoto}
          onClose={() => setEditingPhoto(null)}
          onUpdate={updatePhoto}
        />
      )}
    </div>
  )
}

// Add Photo Form Component
function AddPhotoForm({ onClose }: { onClose: () => void }) {
  const { addPhoto, categories } = useAdmin()
  const [formData, setFormData] = useState({
    title: '',
    src: '',
    category: 'landscapes',
    description: '',
    isPaid: false,
    price: 0
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    addPhoto(formData)
    onClose()
    setFormData({
      title: '',
      src: '',
      category: 'landscapes',
      description: '',
      isPaid: false,
      price: 0
    })
  }

  return (
    <div>
      <DialogHeader>
        <DialogTitle>Add New Photo</DialogTitle>
        <DialogDescription>
          Add a new photo to your portfolio collection.
        </DialogDescription>
      </DialogHeader>
      <form onSubmit={handleSubmit} className="space-y-4 mt-4">
        <div>
          <label className="text-sm font-medium">Photo URL</label>
          <Input
            value={formData.src}
            onChange={(e) => setFormData({ ...formData, src: e.target.value })}
            placeholder="https://example.com/photo.jpg"
            required
          />
        </div>
        <div>
          <label className="text-sm font-medium">Title</label>
          <Input
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            placeholder="Photo title"
            required
          />
        </div>
        <div>
          <label className="text-sm font-medium">Category</label>
          <select
            className="w-full p-2 border rounded"
            value={formData.category}
            onChange={(e) => setFormData({ ...formData, category: e.target.value })}
          >
            {categories.map(cat => (
              <option key={cat.id} value={cat.id}>{cat.name}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="text-sm font-medium">Description</label>
          <Textarea
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Photo description"
          />
        </div>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={formData.isPaid}
            onChange={(e) => setFormData({ ...formData, isPaid: e.target.checked })}
          />
          <label className="text-sm font-medium">Premium Photo</label>
        </div>
        {formData.isPaid && (
          <div>
            <label className="text-sm font-medium">Price ($)</label>
            <Input
              type="number"
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
              min="0"
              step="0.01"
            />
          </div>
        )}
        <div className="flex space-x-2">
          <Button type="submit" className="bg-orange-500 hover:bg-orange-600">
            Add Photo
          </Button>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </form>
    </div>
  )
}

// Pricing Row Component
function PricingRow({
  photo,
  onUpdatePrice
}: {
  photo: Photo
  onUpdatePrice: (id: string, price: number) => void
}) {
  const [newPrice, setNewPrice] = useState(photo.price?.toString() || '0')

  const handleUpdatePrice = () => {
    onUpdatePrice(photo.id, Number(newPrice))
  }

  return (
    <TableRow>
      <TableCell>
        <img src={photo.src} alt={photo.title} className="w-12 h-12 object-cover rounded" />
      </TableCell>
      <TableCell>{photo.title}</TableCell>
      <TableCell>${photo.price}</TableCell>
      <TableCell>
        <div className="flex items-center space-x-2">
          <Input
            type="number"
            value={newPrice}
            onChange={(e) => setNewPrice(e.target.value)}
            className="w-20"
            min="0"
            step="0.01"
          />
          <Button size="sm" onClick={handleUpdatePrice}>
            Update
          </Button>
        </div>
      </TableCell>
    </TableRow>
  )
}

// Edit Photo Dialog Component
function EditPhotoDialog({
  photo,
  onClose,
  onUpdate
}: {
  photo: Photo
  onClose: () => void
  onUpdate: (id: string, updates: Partial<Photo>) => void
}) {
  const [formData, setFormData] = useState({
    title: photo.title,
    description: photo.description || '',
    isPaid: photo.isPaid,
    price: photo.price || 0
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onUpdate(photo.id, formData)
    onClose()
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Photo</DialogTitle>
          <DialogDescription>
            Update photo details and pricing.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="text-sm font-medium">Title</label>
            <Input
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
            />
          </div>
          <div>
            <label className="text-sm font-medium">Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={formData.isPaid}
              onChange={(e) => setFormData({ ...formData, isPaid: e.target.checked })}
            />
            <label className="text-sm font-medium">Premium Photo</label>
          </div>
          {formData.isPaid && (
            <div>
              <label className="text-sm font-medium">Price ($)</label>
              <Input
                type="number"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
                min="0"
                step="0.01"
              />
            </div>
          )}
          <div className="flex space-x-2">
            <Button type="submit" className="bg-orange-500 hover:bg-orange-600">
              Update Photo
            </Button>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
