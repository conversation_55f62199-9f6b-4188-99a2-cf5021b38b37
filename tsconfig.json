{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "jsxImportSource": "same-runtime/dist", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "build/types/**/*.ts", "next-env.d.ts", "out/types/**/*.ts"], "exclude": ["node_modules"]}